from ..db import get_session
import json
import os
from dotenv import load_dotenv
from sqlmodel import Session, select
from ..db import engine
from ..models import *
from ..models import BehaviorType  # Explicitly import BehaviorType
from datetime import datetime, timezone
import random
from sqlalchemy import and_
from ..utils.score_calculator import (
    calculate_competency_scores,
    calculate_factor_scores_for_evaluation,
    calculate_category_scores_for_evaluation
)

# Load environment variables
load_dotenv()

def seed_roles():
    roles = ["<PERSON><PERSON><PERSON>", "Consultor", "Manager", "Director", "<PERSON>cio"]
    with get_session() as session:
        for role_name in roles:
            if not session.query(Role).filter_by(name=role_name).first():
                session.add(Role(name=role_name))
        session.commit()
    print("Roles seeded.")

def seed_performance_competencies():
    with open("assets/competencias_performance.json", "r", encoding="utf-8") as f:
        data = json.load(f)

    with get_session() as session:
        for comp in data:
            if not session.query(Competency).filter_by(code=str(comp["code"])).first():
                # Map the JSON category to the CompetencyCategory enum
                category_map = {
                    "competencias_tecnicas": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "competencias_comportamentales": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "feedback_cliente": CompetencyCategory.FEEDBACK_CLIENTE,
                    "feedback_manager": CompetencyCategory.FEEDBACK_MANAGER,
                    "aprendizaje": CompetencyCategory.APRENDIZAJE
                }

                # Convert the category to lowercase and remove any spaces
                category_key = comp["categoria"].lower().strip()
                category = category_map.get(category_key)

                if not category:
                    print(f"[WARNING] Unknown category: {comp['categoria']} for competency {comp['code']}")
                    continue

                new_comp = Competency(
                    code=str(comp["code"]),
                    name=comp["name"],
                    description=comp.get("description", ""),
                    factor=comp.get("factor"),
                    group=comp.get("group"),
                    category=category
                )
                session.add(new_comp)
                session.flush()

                # Process expert behavior questions
                for q in comp.get("expert_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.EXPERT
                        )
                        session.add(question)

                # Process talented behavior questions
                for q in comp.get("talented_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.TALENTED
                        )
                        session.add(question)

                # Process low skill behavior questions
                for q in comp.get("low_skill_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.LOW_SKILL
                        )
                        session.add(question)
        session.commit()
    print("Technical competencies seeded.")

def seed_potential_competencies():
    with open("assets/competencias_potencial.json", "r", encoding="utf-8") as f:
        data = json.load(f)

    with get_session() as session:
        for comp in data:
            if not session.query(Competency).filter_by(code=str(comp["code"])).first():
                category_map = {
                    "competencias_tecnicas": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "competencias_comportamentales": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "feedback_cliente": CompetencyCategory.FEEDBACK_CLIENTE,
                    "feedback_manager": CompetencyCategory.FEEDBACK_MANAGER,
                    "aprendizaje": CompetencyCategory.APRENDIZAJE
                }

                category_key = comp["categoria"].lower().strip()
                category = category_map.get(category_key)

                if not category:
                    print(f"[WARNING] Unknown category: {comp['categoria']} for competency {comp['code']}")
                    continue

                new_comp = Competency(
                    code=str(comp["code"]),
                    name=comp["name"],
                    description=comp.get("description", ""),
                    factor=comp.get("factor"),
                    group=comp.get("group"),
                    category=category
                )
                session.add(new_comp)
                session.flush()

                # Process expert behavior questions
                for q in comp.get("expert_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.EXPERT
                        )
                        session.add(question)

                # Process talented behavior questions
                for q in comp.get("talented_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.TALENTED
                        )
                        session.add(question)

                # Process low skill behavior questions
                for q in comp.get("low_skill_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.LOW_SKILL
                        )
                        session.add(question)
        session.commit()
    print("Potential competencies seeded.")

def seed_competency_role_map_from_json():
    with open("assets/role_comp_map.json", "r", encoding="utf-8") as f:
        role_comp_map = json.load(f)

    with get_session() as session:
        for entry in role_comp_map:
            role = session.query(Role).filter_by(name=entry["role"]).first()
            if not role:
                print(f"[WARNING] Role not found: {entry['role']}")
                continue

            for comp_code in entry["competencies"]:
                competency = session.query(Competency).filter_by(code=str(comp_code)).first()
                if not competency:
                    print(f"[WARNING] Competency code not found: {comp_code}")
                    continue

                exists = session.query(CompetencyRoleMap).filter_by(
                    role_id=role.id,
                    competency_id=competency.id
                ).first()

                if not exists:
                    session.add(CompetencyRoleMap(
                        role_id=role.id,
                        competency_id=competency.id,
                        weight=1.0
                    ))

        session.commit()
    print("CompetencyRoleMap seeded from JSON.")

def seed_users_and_projects():
    # Get user Azure ID from environment variable or use a default
    user_azure_id = os.getenv("USER_AZURE_ID")

    users_data = [
        {"name": "Bruno Bolla Pons", "email": "<EMAIL>", "azure_id": user_azure_id, "role_name": "Analista", "main_evaluator": True},
        {"name": "Ana García López", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000001", "role_name": "Analista", "main_evaluator": False},
        {"name": "Carlos Ruiz Martínez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000002", "role_name": "Consultor", "main_evaluator": True},
        {"name": "Laura Torres Jiménez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000005", "role_name": "Analista", "main_evaluator": False},
        {"name": "Javier Moreno Rodríguez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000006", "role_name": "Consultor", "main_evaluator": True},
    ]

    projects_data = [
        {"code": "PRJ001", "name": "AI Strategy", "start_date": datetime(2024, 1, 1), "status": ProjectStatus.ACTIVE},
        {"code": "PRJ002", "name": "Digital Transformation", "start_date": datetime(2024, 2, 15), "status": ProjectStatus.ACTIVE},
    ]

    user_project_links = [
        #p1
        ("<EMAIL>", "PRJ001"),
        ("<EMAIL>", "PRJ001"),
        ("<EMAIL>", "PRJ001"),

        # Project 2 (Digital Transformation)
        ("<EMAIL>", "PRJ002"),
        ("<EMAIL>", "PRJ002"),
        ("<EMAIL>", "PRJ002"),
    ]

    with get_session() as session:
        # First ensure we have roles
        roles = session.query(Role).all()
        if not roles:
            print("Warning: No roles found. Please run seed_roles() first.")
            return

        # Create users with roles
        for user_data in users_data:
            if not session.query(User).filter_by(email=user_data["email"]).first():
                role = session.query(Role).filter_by(name=user_data["role_name"]).first()
                if not role:
                    print(f"Warning: Role '{user_data['role_name']}' not found for user {user_data['email']}")
                    continue

                role_name = user_data.pop("role_name")
                user_data["role_id"] = role.id

                session.add(User(**user_data))
        session.commit()

        # Create projects
        for proj in projects_data:
            if not session.query(Project).filter_by(code=proj["code"]).first():
                session.add(Project(**proj))
        session.commit()

        # Create user-project links
        for email, code in user_project_links:
            user = session.query(User).filter_by(email=email).first()
            project = session.query(Project).filter_by(code=code).first()
            if user and project:
                if not session.query(UserProject).filter_by(user_id=user.id, project_id=project.id).first():
                    session.add(UserProject(user_id=user.id, project_id=project.id))
        session.commit()

def seed_evaluations():
    with get_session() as session:
        # Fetch all required data upfront
        users = session.query(User).all()
        projects = session.query(Project).all()
        roles = session.query(Role).all()

        batch_size = 100  # Process evaluations in batches
        current_batch = []

        # Get user 1 specifically
        user_1 = session.query(User).filter_by(id=1).first()
        if user_1:
            print(f"Creating diverse evaluations for user: {user_1.name} (ID: {user_1.id})")

            # Get competencies mapped to user_1's role
            user_1_competencies = (
                session.query(Competency)
                .join(CompetencyRoleMap)
                .where(CompetencyRoleMap.role_id == user_1.role_id)
                .all()
            )

            # Get questions only for user_1's role competencies
            questions = (
                session.query(EvaluationQuestion)
                .join(Competency)
                .join(CompetencyRoleMap)
                .where(CompetencyRoleMap.role_id == user_1.role_id)
                .all()
            )

            # Group questions by competency_id and behavior_type for faster access
            questions_by_competency = {}
            for q in questions:
                if q.competency_id not in questions_by_competency:
                    questions_by_competency[q.competency_id] = []
                questions_by_competency[q.competency_id].append(q)

            # Create evaluations for user 1 for each project
            for project in projects:
                print(f"Creating evaluation for project: {project.name} (ID: {project.id})")

                # Create self-evaluation for user 1 for this project with varied responses
                self_eval = Evaluation(
                    evaluation_type=EvaluationType.PERFORMANCE,
                    evaluator_type=EvaluatorType.SELF,
                    evaluator_id=user_1.id,
                    evaluatee_id=user_1.id,
                    project_id=project.id,
                    status=EvaluationStatus.DRAFT if random.random() < 0.5 else EvaluationStatus.SUBMITTED,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    evaluatee_role_id=user_1.role_id  # Add evaluatee's role at evaluation time
                )
                session.add(self_eval)
                session.flush()  # Get the ID

                # Create answers with varied responses based on behavior type
                answers = []
                for question in questions:
                    # Determine response based on behavior type
                    if question.behavior_type == BehaviorType.EXPERT:
                        # Expert behavior - 70% positive
                        rand = random.random()
                        if rand < 0.7:
                            response = ResponseType.SI
                        elif rand < 0.85:  # 15% sometimes
                            response = ResponseType.A_VECES
                        else:  # 15% no
                            response = ResponseType.NO
                    elif question.behavior_type == BehaviorType.TALENTED:
                        # Talented behavior - 50% positive
                        rand = random.random()
                        if rand < 0.5:
                            response = ResponseType.SI
                        elif rand < 0.7:  # 20% sometimes
                            response = ResponseType.A_VECES
                        else:  # 30% no
                            response = ResponseType.NO
                    else:
                        # Low skill behavior - 30% positive
                        rand = random.random()
                        if rand < 0.3:
                            response = ResponseType.SI
                        elif rand < 0.6:  # 30% sometimes
                            response = ResponseType.A_VECES
                        else:  # 40% no
                            response = ResponseType.NO

                    # Simple, realistic self-evaluation comments
                    simple_comments = [
                        "Creo que lo hago bien en general",
                        "Necesito mejorar en esto",
                        "Me siento cómodo con esta área",
                        "A veces me cuesta un poco",
                        "Es una de mis fortalezas",
                        "Tengo que trabajar más en esto",
                        "Lo manejo bastante bien",
                        "Me falta práctica aquí",
                        "Estoy mejorando poco a poco",
                        "Se me da natural",
                        "Necesito más experiencia",
                        "Lo hago cuando es necesario"
                    ]

                    answer = EvaluationAnswer(
                        evaluation_id=self_eval.id,
                        question_id=question.id,
                        response=response,
                        comment=random.choice(simple_comments)
                    )
                    answers.append(answer)

                session.bulk_save_objects(answers)
                current_batch.append(self_eval.id)

                # Create peer evaluations from other users to user 1 with varied patterns
                project_users = [
                    up.user for up in session.query(UserProject)
                    .filter_by(project_id=project.id)
                    .all() if up.user.id != user_1.id
                ]

                # Create peer evaluations with different patterns for each peer
                for idx, peer in enumerate(project_users[:3]):  # Increase to 3 peers per project
                    peer_eval = Evaluation(
                        evaluation_type=EvaluationType.PERFORMANCE,
                        evaluator_type=EvaluatorType.PEER,
                        evaluator_id=peer.id,
                        evaluatee_id=user_1.id,
                        project_id=project.id,
                        status=EvaluationStatus.SUBMITTED,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        evaluatee_role_id=user_1.role_id  # Add evaluatee's role at evaluation time
                    )
                    session.add(peer_eval)
                    session.flush()

                    # Create answers with different patterns for each peer
                    peer_answers = []
                    for question in questions:
                        # Adjust response probability based on peer's role
                        peer_role = session.query(Role).filter_by(id=peer.role_id).first()
                        if peer_role.name in ["Analista", "Consultor"]:
                            # Junior roles - better at technical, worse at leadership
                            if question.competency.category == CompetencyCategory.COMPETENCIAS_TECNICAS:
                                # 85% positive
                                rand = random.random()
                                if rand < 0.85:
                                    response = ResponseType.SI
                                elif rand < 0.95:  # 10% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 5% no
                                    response = ResponseType.NO
                            else:
                                # 50% positive
                                rand = random.random()
                                if rand < 0.5:
                                    response = ResponseType.SI
                                elif rand < 0.7:  # 20% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 30% no
                                    response = ResponseType.NO
                        else:
                            # Senior roles - better at leadership, mixed at technical
                            if question.competency.category == CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES:
                                # 90% positive
                                rand = random.random()
                                if rand < 0.9:
                                    response = ResponseType.SI
                                elif rand < 0.95:  # 5% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 5% no
                                    response = ResponseType.NO
                            else:
                                # 70% positive
                                rand = random.random()
                                if rand < 0.7:
                                    response = ResponseType.SI
                                elif rand < 0.85:  # 15% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 15% no
                                    response = ResponseType.NO

                        # Simple, realistic peer evaluation comments
                        peer_comments = [
                            "Trabaja muy bien en equipo",
                            "Siempre está dispuesto a ayudar",
                            "Tiene buen conocimiento técnico",
                            "A veces le cuesta comunicar ideas",
                            "Es muy organizado en sus tareas",
                            "Podría ser más proactivo",
                            "Maneja bien la presión",
                            "Necesita más confianza",
                            "Es muy detallista",
                            "Buen compañero de trabajo",
                            "Le falta experiencia en esto",
                            "Siempre cumple los plazos"
                        ]

                        answer = EvaluationAnswer(
                            evaluation_id=peer_eval.id,
                            question_id=question.id,
                            response=response,
                            comment=random.choice(peer_comments)
                        )
                        peer_answers.append(answer)

                    session.bulk_save_objects(peer_answers)
                    current_batch.append(peer_eval.id)

                # Process batch if it reaches the batch size
                if len(current_batch) >= batch_size:
                    session.commit()
                    process_evaluation_batch(current_batch, session)
                    current_batch = []

        # Generate random evaluations for other users
        for project in projects:
            project_users = [
                up.user for up in session.query(UserProject)
                .filter_by(project_id=project.id)
                .all() if up.user.id != 1  # Skip user 1 as we've already created custom evals
            ]

            for user in project_users:
                # Get competencies mapped to user's role
                user_competencies = (
                    session.query(Competency)
                    .join(CompetencyRoleMap)
                    .where(CompetencyRoleMap.role_id == user.role_id)
                    .all()
                )

                # Get questions only for user's role competencies
                user_questions = (
                    session.query(EvaluationQuestion)
                    .join(Competency)
                    .join(CompetencyRoleMap)
                    .where(CompetencyRoleMap.role_id == user.role_id)
                    .all()
                )

                # Self evaluation (50% chance)
                if random.random() < 0.50:
                    self_eval = Evaluation(
                        evaluation_type=EvaluationType.PERFORMANCE,
                        evaluator_type=EvaluatorType.SELF,
                        evaluator_id=user.id,
                        evaluatee_id=user.id,
                        project_id=project.id,
                        status=EvaluationStatus.DRAFT if random.random() < 0.5 else EvaluationStatus.SUBMITTED,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        evaluatee_role_id=user.role_id  # Add evaluatee's role at evaluation time
                    )
                    session.add(self_eval)
                    session.flush()  # Get the ID

                    # Create all answers for this evaluation at once
                    answers = []
                    for question in user_questions:
                        # 80% positive, 10% sometimes, 10% no
                        rand = random.random()
                        if rand < 0.8:
                            response = ResponseType.SI
                        elif rand < 0.9:
                            response = ResponseType.A_VECES
                        else:
                            response = ResponseType.NO

                        # Simple, realistic self-evaluation comments
                        self_comments = [
                            "Creo que lo hago bien",
                            "Necesito mejorar esto",
                            "Me siento cómodo",
                            "A veces me cuesta",
                            "Es mi fuerte",
                            "Tengo que practicar más",
                            "Lo manejo bien",
                            "Me falta experiencia",
                            "Estoy mejorando",
                            "Se me da bien",
                            "Necesito ayuda aquí",
                            "Lo hago cuando puedo"
                        ]

                        answer = EvaluationAnswer(
                            evaluation_id=self_eval.id,
                            question_id=question.id,
                            response=response,
                            comment=random.choice(self_comments)
                        )
                        answers.append(answer)

                    session.bulk_save_objects(answers)
                    current_batch.append(self_eval.id)

                    # Process batch if it reaches the batch size
                    if len(current_batch) >= batch_size:
                        session.commit()
                        process_evaluation_batch(current_batch, session)
                        current_batch = []

        # Process any remaining evaluations
        if current_batch:
            session.commit()
            process_evaluation_batch(current_batch, session)

    print("Evaluations and scores seeded with diverse patterns.")

def process_evaluation_batch(eval_ids: List[int], session: Session):
    """Process a batch of evaluations to calculate their scores."""
    for eval_id in eval_ids:
        try:
            # Calculate all scores in one transaction
            comp_scores = calculate_competency_scores(eval_id, session)
            session.bulk_save_objects(comp_scores)

            factor_scores = calculate_factor_scores_for_evaluation(eval_id, session)
            if factor_scores:
                session.bulk_save_objects(factor_scores)

            category_scores = calculate_category_scores_for_evaluation(eval_id, session)
            if category_scores:
                session.bulk_save_objects(category_scores)

            session.commit()
            print(f"✓ Processed scores for evaluation {eval_id}")

        except Exception as e:
            print(f"✗ Error processing scores for evaluation {eval_id}: {str(e)}")
            session.rollback()
            continue

def seed_action_plans():
    """Seed comprehensive action plans for user ID 1 (Bruno Bolla Pons) across multiple evaluations."""
    with get_session() as session:
        # Get user 1 (Bruno Bolla Pons)
        user_1 = session.get(User, 1)
        if not user_1:
            print("User 1 not found. Please run seed_users_and_projects() first.")
            return

        # Get evaluations for user 1 where he is the evaluatee
        evaluations = session.exec(
            select(Evaluation).where(Evaluation.evaluatee_id == 1)
        ).all()

        if not evaluations:
            print("No evaluations found for user 1. Please run seed_evaluations() first.")
            return

        print(f"Found {len(evaluations)} evaluations for user {user_1.name}")

        # Define action plan templates for different evaluators and contexts
        action_plan_templates = {
            # Manager feedback (formal, strategic)
            "manager": {
                "Competencias Comportamentales": {
                    "Desarrollo personal": {
                        "category": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                        "texts": [
                            """Bruno, después de revisar tu desempeño en este proyecto, creo que hay oportunidades importantes de crecimiento.

Tu capacidad técnica es sólida, pero necesitas trabajar en tu liderazgo personal. Te sugiero que tomes más iniciativa en las reuniones de equipo y que compartas tus ideas con más confianza.

También he notado que podrías mejorar tu gestión del tiempo. A veces te enfocas demasiado en los detalles y pierdes de vista los plazos importantes.

Para el próximo trimestre, me gustaría que te enfoques en desarrollar tu presencia ejecutiva. Practica presentar tus ideas de forma más concisa y directa.

Estoy aquí para apoyarte en este desarrollo. Programemos una reunión mensual para hacer seguimiento.""",

                            """He estado observando tu progreso y veo mejoras, pero aún hay áreas donde puedes crecer más.

Tu colaboración con el equipo ha mejorado, pero creo que puedes ser más proactivo en buscar feedback. No esperes a que te lo den, pídelo activamente.

También necesitas trabajar en tu adaptabilidad. Cuando cambian los requerimientos del proyecto, a veces te cuesta ajustarte rápidamente.

Te recomiendo que participes en más proyectos transversales para ganar experiencia en diferentes contextos. Esto te ayudará a ser más flexible.

Vamos a establecer objetivos específicos para los próximos meses y haremos seguimiento regular."""
                        ]
                    }
                },
                "Competencias Tecnicas": {
                    "Bloque 0: Preparar el Engagement": {
                        "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                        "texts": [
                            """Tu preparación para los proyectos necesita ser más estratégica. Veo que dominas los aspectos técnicos, pero te falta visión de negocio.

Antes de cada engagement, dedica tiempo a entender no solo qué hace el cliente, sino por qué lo hace y cuáles son sus principales desafíos estratégicos.

Te sugiero que leas más sobre las industrias de nuestros clientes. Suscríbete a publicaciones especializadas y mantente al día con las tendencias del sector.

También es importante que entiendas mejor nuestro posicionamiento como firma. Revisa casos similares que hayamos hecho antes y aprende de esas experiencias.

Para el próximo proyecto, quiero que me presentes un análisis del contexto del cliente antes de empezar el trabajo técnico.""",

                            """He notado que tu conocimiento del cliente ha mejorado, pero aún puedes profundizar más en la comprensión del negocio.

Cuando prepares un proyecto, no te limites a leer la documentación básica. Habla con gente que haya trabajado con ese cliente antes y entiende su cultura organizacional.

También necesitas mejorar tu conocimiento de frameworks de management. Estos te ayudarán a estructurar mejor tu análisis y a comunicarte más efectivamente con el cliente.

Te voy a asignar a un proyecto más complejo el próximo mes para que puedas practicar estas habilidades en un contexto real.

Espero ver una mejora significativa en tu preparación estratégica."""
                        ]
                    },
                    "Bloque 1: Enmarcar el Problema": {
                        "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                        "texts": [
                            """Tu capacidad para estructurar problemas ha mejorado, pero necesitas ser más sistemático en tu enfoque.

Cuando recibas un encargo, no te lances directamente al análisis. Tómate tiempo para entender realmente qué está pidiendo el cliente y por qué.

Desarrolla el hábito de crear hipótesis claras antes de empezar cualquier trabajo. Esto te ayudará a mantener el foco y a ser más eficiente.

También necesitas mejorar tu comunicación con el equipo. Asegúrate de que todos entiendan el problema de la misma manera antes de dividir el trabajo.

Para el próximo proyecto, quiero que lideres la sesión de framing del problema. Será una buena oportunidad para practicar.""",

                            """Veo progreso en tu capacidad de definir problemas, pero aún puedes ser más incisivo en tus preguntas.

No tengas miedo de cuestionar los supuestos del cliente. A veces el problema real no es el que nos presentan inicialmente.

Trabaja en desarrollar tu pensamiento crítico. Pregúntate siempre si hay otras formas de ver el problema y si estamos atacando la causa raíz.

También mejora tu capacidad de síntesis. Puedes tomar mucha información, pero necesitas ser mejor extrayendo lo esencial.

Te voy a dar más responsabilidad en la definición de problemas en los próximos proyectos."""
                        ]
                    }
                }
            },

            # Peer feedback (collaborative, practical)
            "peer": {
                "Competencias Comportamentales": {
                    "Desarrollo personal": {
                        "category": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                        "texts": [
                            """Bruno, trabajar contigo en este proyecto ha sido genial. Eres muy colaborativo y siempre estás dispuesto a ayudar.

Una cosa que he notado es que a veces eres demasiado perfeccionista. Está bien querer hacer las cosas bien, pero a veces es mejor avanzar con algo bueno que quedarse atascado buscando la perfección.

También creo que podrías ser más vocal en las reuniones. Tienes buenas ideas, pero no siempre las compartes. El equipo se beneficiaría de escuchar más tu perspectiva.

En general, creo que tienes mucho potencial y me gusta trabajar contigo. Solo necesitas un poco más de confianza en ti mismo.

Si necesitas practicar presentar ideas, podemos hacerlo juntos antes de las reuniones importantes.""",

                            """Ha sido muy bueno trabajar contigo en este proyecto. Eres muy organizado y siempre cumples con tus compromisos.

He visto que a veces te cuesta adaptarte cuando cambian las prioridades del proyecto. Entiendo que puede ser frustrante, pero es parte del trabajo en consultoría.

También creo que podrías ser más proactivo pidiendo ayuda cuando la necesitas. No hay problema en no saber algo, todos estamos aprendiendo.

Me gusta tu atención al detalle, pero a veces creo que te enfocas demasiado en cosas pequeñas y pierdes de vista el panorama general.

En el próximo proyecto me gustaría que trabajáramos más de cerca. Creo que podemos aprender mucho el uno del otro."""
                        ]
                    }
                },
                "Competencias Tecnicas": {
                    "Bloque 2: Diseñar el Analisis": {
                        "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                        "texts": [
                            """Bruno, me gusta cómo piensas los análisis. Siempre eres muy metódico y consideras diferentes ángulos.

Una sugerencia que tengo es que podrías ser más ágil en tu enfoque. A veces diseñas análisis muy complejos cuando algo más simple podría funcionar igual de bien.

También he notado que a veces te cuesta explicar tu lógica analítica a otros. Practica explicar tus ideas de forma más simple, especialmente cuando hables con el cliente.

Tu conocimiento técnico es muy bueno, pero creo que podrías mejorar en la parte de storytelling con datos.

Si quieres, podemos revisar juntos algunos de tus análisis y ver cómo hacerlos más claros.""",

                            """Trabajar contigo en los análisis es siempre productivo. Eres muy cuidadoso con los detalles y eso se nota en la calidad de tu trabajo.

Creo que podrías ser más creativo en tu enfoque analítico. A veces hay formas más innovadoras de abordar un problema que las que usamos tradicionalmente.

También he visto que a veces te toma mucho tiempo validar tu diseño. Está bien ser cuidadoso, pero también es importante ser eficiente.

Me gusta que siempre documentas bien tu trabajo. Eso hace que sea fácil para otros entender y continuar tu análisis.

Para el próximo proyecto, me gustaría que probáramos algunas técnicas nuevas de análisis que he estado aprendiendo."""
                        ]
                    },
                    "Bloque 3: Recopilar Datos": {
                        "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                        "texts": [
                            """Bruno, eres muy bueno organizando datos. Siempre dejas todo muy limpio y bien documentado.

Una cosa que podrías mejorar es ser más eficiente en la recolección inicial. A veces pides más datos de los que realmente necesitas.

También he notado que podrías ser más proactivo identificando problemas de calidad en los datos. Cuanto antes los detectemos, mejor.

Tu trabajo de limpieza de datos es excelente, pero creo que podrías automatizar más procesos para ser más eficiente.

Si quieres, puedo enseñarte algunas técnicas que uso para acelerar el procesamiento de datos.""",

                            """Me gusta trabajar contigo en temas de datos porque eres muy cuidadoso y meticuloso.

Creo que podrías ser más estratégico en cómo solicitas información al cliente. A veces podemos conseguir lo que necesitamos con menos iteraciones.

También he visto que eres muy bueno detectando inconsistencias en los datos. Esa habilidad es muy valiosa para el equipo.

Una sugerencia es que podrías ser más creativo buscando fuentes alternativas de datos cuando la información principal no está disponible.

En el próximo proyecto me gustaría que lideraras la estrategia de recolección de datos."""
                        ]
                    }
                }
            },

            # Self-evaluation feedback (reflective, personal)
            "self": {
                "Aprendizaje": {
                    "Desarrollo personal": {
                        "category": CompetencyCategory.APRENDIZAJE,
                        "texts": [
                            """Reflexionando sobre mi desempeño en este proyecto, creo que he crecido mucho pero aún tengo áreas donde mejorar.

Me siento más cómodo con los aspectos técnicos del trabajo, pero sé que necesito desarrollar más mis habilidades de comunicación y liderazgo.

He notado que aprendo mejor cuando trabajo en equipo y puedo hacer preguntas. Me gustaría buscar más oportunidades de mentoring.

También creo que necesito ser más sistemático en mi aprendizaje. A veces aprendo cosas de forma reactiva en lugar de tener un plan claro.

Para los próximos meses, quiero enfocarme en desarrollar mi presencia en reuniones y mi capacidad de influir sin autoridad formal.""",

                            """Al final de este proyecto, siento que he aprendido mucho sobre el trabajo en consultoría, pero también veo claramente dónde necesito mejorar.

Mi mayor fortaleza sigue siendo el análisis técnico, pero necesito trabajar en traducir esos análisis en insights de negocio.

He mejorado en mi capacidad de trabajar bajo presión, pero aún me cuesta gestionar múltiples prioridades al mismo tiempo.

Creo que necesito ser más proactivo buscando feedback y oportunidades de desarrollo. No puedo esperar a que me las den.

Mi objetivo para el próximo trimestre es tomar más iniciativa en los proyectos y desarrollar mi capacidad de pensamiento estratégico."""
                        ]
                    }
                }
            }
        }

        # Create action plans for multiple evaluations with different evaluators
        created_count = 0

        for i, evaluation in enumerate(evaluations[:4]):  # Limit to first 4 evaluations
            print(f"Creating action plans for evaluation ID: {evaluation.id} (Project: {evaluation.project_id})")

            # Determine evaluator type and select appropriate templates
            if evaluation.evaluator_type == EvaluatorType.SELF:
                evaluator_type = "self"
                evaluator_id = evaluation.evaluator_id
            elif evaluation.evaluator_type == EvaluatorType.PEER:
                evaluator_type = "peer"
                evaluator_id = evaluation.evaluator_id
            else:  # Manager or other
                evaluator_type = "manager"
                evaluator_id = evaluation.evaluator_id or 3  # Default to Carlos if no evaluator

            # Select templates for this evaluation
            templates = action_plan_templates.get(evaluator_type, action_plan_templates["manager"])

            # Create action plans for this evaluation
            for category_name, factors in templates.items():
                for factor_name, plan_data in factors.items():
                    # Check if action plan already exists
                    existing_plan = session.exec(
                        select(ActionPlan).where(
                            ActionPlan.evaluation_id == evaluation.id,
                            ActionPlan.factor == factor_name,
                            ActionPlan.category == plan_data["category"]
                        )
                    ).first()

                    if not existing_plan:
                        # Select text based on evaluation index to add variety
                        text_index = i % len(plan_data["texts"])
                        selected_text = plan_data["texts"][text_index]

                        action_plan = ActionPlan(
                            evaluation_id=evaluation.id,
                            category=plan_data["category"],
                            factor=factor_name,
                            action_plan_text=selected_text,
                            evaluator_id=evaluator_id,
                            created_at=datetime.now(timezone.utc)
                        )
                        session.add(action_plan)
                        created_count += 1
                        print(f"  Created {evaluator_type} action plan: {category_name} - {factor_name}")

        # Add some additional technical competency action plans for variety
        additional_technical_plans = {
            "Bloque 4: Interpretar los Resultados": {
                "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                "texts": [
                    """Bruno, tu capacidad de análisis es buena, pero necesitas mejorar cómo interpretas los resultados.

No te quedes solo en describir lo que ves en los números. Trata de entender qué historia están contando y qué implicaciones tienen para el negocio del cliente.

Cuando veas algo inesperado en los datos, no lo ignores. Esas anomalías a menudo son donde están los insights más valiosos.

También practica conectar diferentes análisis para crear una narrativa coherente. Los datos aislados no sirven de mucho.

Para el próximo proyecto, quiero que me presentes no solo los resultados, sino también tu interpretación de lo que significan.""",

                    """He visto mejoras en tu capacidad de interpretar datos, pero aún puedes profundizar más en el análisis.

Cuando tengas resultados, pregúntate siempre 'y qué?' hasta que llegues a algo accionable para el cliente.

También creo que podrías ser mejor identificando patrones y tendencias que no son obvios a primera vista.

Tu trabajo técnico es sólido, pero necesitas desarrollar más tu intuición de negocio para interpretar mejor los resultados.

Vamos a trabajar juntos en algunos ejercicios de interpretación de datos para que practiques."""
                ]
            },
            "Bloque 5: Presentación de ideas": {
                "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                "texts": [
                    """Bruno, tu trabajo analítico es muy bueno, pero necesitas mejorar cómo lo comunicas.

Tus slides a veces tienen demasiada información. Recuerda que cada slide debe tener un mensaje claro y específico.

También practica tu storytelling. Los datos son importantes, pero la forma en que cuentas la historia es lo que hace que la gente actúe.

Cuando presentes, mantén contacto visual con la audiencia y habla con confianza. Tu conocimiento es sólido, solo necesitas proyectarlo mejor.

Te voy a dar más oportunidades de presentar en las próximas reuniones con clientes para que practiques.""",

                    """Veo que has mejorado en tus presentaciones, pero aún hay espacio para crecer.

Tus slides están mejor estructuradas, pero podrías ser más impactante en tus títulos. Que cada título cuente una historia.

También trabaja en anticipar preguntas. Cuando presentes, piensa qué podría preguntar la audiencia y ten las respuestas preparadas.

Tu conocimiento técnico es excelente, pero necesitas traducirlo mejor para audiencias no técnicas.

Practiquemos juntos antes de tu próxima presentación importante."""
                ]
            }
        }

        # Add additional technical plans to some evaluations
        for i, evaluation in enumerate(evaluations[1:3]):  # Add to evaluations 2 and 3
            for factor_name, plan_data in additional_technical_plans.items():
                existing_plan = session.exec(
                    select(ActionPlan).where(
                        ActionPlan.evaluation_id == evaluation.id,
                        ActionPlan.factor == factor_name,
                        ActionPlan.category == plan_data["category"]
                    )
                ).first()

                if not existing_plan:
                    text_index = i % len(plan_data["texts"])
                    selected_text = plan_data["texts"][text_index]

                    action_plan = ActionPlan(
                        evaluation_id=evaluation.id,
                        category=plan_data["category"],
                        factor=factor_name,
                        action_plan_text=selected_text,
                        evaluator_id=3,  # Carlos as default manager
                        created_at=datetime.now(timezone.utc)
                    )
                    session.add(action_plan)
                    created_count += 1
                    print(f"  Created additional technical action plan: {factor_name}")

        session.commit()
        print(f"Successfully created {created_count} action plans for user {user_1.name} across {len(evaluations)} evaluations")

if __name__ == "__main__":
    seed_roles()
    seed_performance_competencies()
    seed_potential_competencies()
    seed_competency_role_map_from_json()
    seed_users_and_projects()
    seed_evaluations()
    seed_action_plans()
