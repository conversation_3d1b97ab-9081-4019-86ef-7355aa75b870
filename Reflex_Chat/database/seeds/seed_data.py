from ..db import get_session
import json
import os
from dotenv import load_dotenv
from sqlmodel import Session, select
from ..db import engine
from ..models import *
from ..models import BehaviorType  # Explicitly import BehaviorType
from datetime import datetime, timezone
import random
from sqlalchemy import and_
from ..utils.score_calculator import (
    calculate_competency_scores,
    calculate_factor_scores_for_evaluation,
    calculate_category_scores_for_evaluation
)

# Load environment variables
load_dotenv()

def seed_roles():
    roles = ["<PERSON><PERSON><PERSON>", "Consultor", "Manager", "Director", "<PERSON>cio"]
    with get_session() as session:
        for role_name in roles:
            if not session.query(Role).filter_by(name=role_name).first():
                session.add(Role(name=role_name))
        session.commit()
    print("Roles seeded.")

def seed_performance_competencies():
    with open("assets/competencias_performance.json", "r", encoding="utf-8") as f:
        data = json.load(f)

    with get_session() as session:
        for comp in data:
            if not session.query(Competency).filter_by(code=str(comp["code"])).first():
                # Map the JSON category to the CompetencyCategory enum
                category_map = {
                    "competencias_tecnicas": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "competencias_comportamentales": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "feedback_cliente": CompetencyCategory.FEEDBACK_CLIENTE,
                    "feedback_manager": CompetencyCategory.FEEDBACK_MANAGER,
                    "aprendizaje": CompetencyCategory.APRENDIZAJE
                }

                # Convert the category to lowercase and remove any spaces
                category_key = comp["categoria"].lower().strip()
                category = category_map.get(category_key)

                if not category:
                    print(f"[WARNING] Unknown category: {comp['categoria']} for competency {comp['code']}")
                    continue

                new_comp = Competency(
                    code=str(comp["code"]),
                    name=comp["name"],
                    description=comp.get("description", ""),
                    factor=comp.get("factor"),
                    group=comp.get("group"),
                    category=category
                )
                session.add(new_comp)
                session.flush()

                # Process expert behavior questions
                for q in comp.get("expert_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.EXPERT
                        )
                        session.add(question)

                # Process talented behavior questions
                for q in comp.get("talented_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.TALENTED
                        )
                        session.add(question)

                # Process low skill behavior questions
                for q in comp.get("low_skill_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.LOW_SKILL
                        )
                        session.add(question)
        session.commit()
    print("Technical competencies seeded.")

def seed_potential_competencies():
    with open("assets/competencias_potencial.json", "r", encoding="utf-8") as f:
        data = json.load(f)

    with get_session() as session:
        for comp in data:
            if not session.query(Competency).filter_by(code=str(comp["code"])).first():
                category_map = {
                    "competencias_tecnicas": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "competencias_comportamentales": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "feedback_cliente": CompetencyCategory.FEEDBACK_CLIENTE,
                    "feedback_manager": CompetencyCategory.FEEDBACK_MANAGER,
                    "aprendizaje": CompetencyCategory.APRENDIZAJE
                }

                category_key = comp["categoria"].lower().strip()
                category = category_map.get(category_key)

                if not category:
                    print(f"[WARNING] Unknown category: {comp['categoria']} for competency {comp['code']}")
                    continue

                new_comp = Competency(
                    code=str(comp["code"]),
                    name=comp["name"],
                    description=comp.get("description", ""),
                    factor=comp.get("factor"),
                    group=comp.get("group"),
                    category=category
                )
                session.add(new_comp)
                session.flush()

                # Process expert behavior questions
                for q in comp.get("expert_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.EXPERT
                        )
                        session.add(question)

                # Process talented behavior questions
                for q in comp.get("talented_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.TALENTED
                        )
                        session.add(question)

                # Process low skill behavior questions
                for q in comp.get("low_skill_behaviour", []):
                    existing_q = session.query(EvaluationQuestion).filter_by(
                        competency_id=new_comp.id,
                        text=q
                    ).first()

                    if not existing_q:
                        question = EvaluationQuestion(
                            competency_id=new_comp.id,
                            text=q,
                            behavior_type=BehaviorType.LOW_SKILL
                        )
                        session.add(question)
        session.commit()
    print("Potential competencies seeded.")

def seed_competency_role_map_from_json():
    with open("assets/role_comp_map.json", "r", encoding="utf-8") as f:
        role_comp_map = json.load(f)

    with get_session() as session:
        for entry in role_comp_map:
            role = session.query(Role).filter_by(name=entry["role"]).first()
            if not role:
                print(f"[WARNING] Role not found: {entry['role']}")
                continue

            for comp_code in entry["competencies"]:
                competency = session.query(Competency).filter_by(code=str(comp_code)).first()
                if not competency:
                    print(f"[WARNING] Competency code not found: {comp_code}")
                    continue

                exists = session.query(CompetencyRoleMap).filter_by(
                    role_id=role.id,
                    competency_id=competency.id
                ).first()

                if not exists:
                    session.add(CompetencyRoleMap(
                        role_id=role.id,
                        competency_id=competency.id,
                        weight=1.0
                    ))

        session.commit()
    print("CompetencyRoleMap seeded from JSON.")

def seed_users_and_projects():
    # Get user Azure ID from environment variable or use a default
    user_azure_id = os.getenv("USER_AZURE_ID")

    users_data = [
        {"name": "Bruno Bolla Pons", "email": "<EMAIL>", "azure_id": user_azure_id, "role_name": "Analista", "main_evaluator": True},
        {"name": "Ana García López", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000001", "role_name": "Analista", "main_evaluator": False},
        {"name": "Carlos Ruiz Martínez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000002", "role_name": "Consultor", "main_evaluator": True},
        {"name": "Laura Torres Jiménez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000005", "role_name": "Analista", "main_evaluator": False},
        {"name": "Javier Moreno Rodríguez", "email": "<EMAIL>", "azure_id": "00000000-0000-0000-0000-000000000006", "role_name": "Consultor", "main_evaluator": True},
    ]

    projects_data = [
        {"code": "PRJ001", "name": "AI Strategy", "start_date": datetime(2024, 1, 1), "status": ProjectStatus.ACTIVE},
        {"code": "PRJ002", "name": "Digital Transformation", "start_date": datetime(2024, 2, 15), "status": ProjectStatus.ACTIVE},
    ]

    user_project_links = [
        #p1
        ("<EMAIL>", "PRJ001"),
        ("<EMAIL>", "PRJ001"),
        ("<EMAIL>", "PRJ001"),

        # Project 2 (Digital Transformation)
        ("<EMAIL>", "PRJ002"),
        ("<EMAIL>", "PRJ002"),
        ("<EMAIL>", "PRJ002"),
    ]

    with get_session() as session:
        # First ensure we have roles
        roles = session.query(Role).all()
        if not roles:
            print("Warning: No roles found. Please run seed_roles() first.")
            return

        # Create users with roles
        for user_data in users_data:
            if not session.query(User).filter_by(email=user_data["email"]).first():
                role = session.query(Role).filter_by(name=user_data["role_name"]).first()
                if not role:
                    print(f"Warning: Role '{user_data['role_name']}' not found for user {user_data['email']}")
                    continue

                role_name = user_data.pop("role_name")
                user_data["role_id"] = role.id

                session.add(User(**user_data))
        session.commit()

        # Create projects
        for proj in projects_data:
            if not session.query(Project).filter_by(code=proj["code"]).first():
                session.add(Project(**proj))
        session.commit()

        # Create user-project links
        for email, code in user_project_links:
            user = session.query(User).filter_by(email=email).first()
            project = session.query(Project).filter_by(code=code).first()
            if user and project:
                if not session.query(UserProject).filter_by(user_id=user.id, project_id=project.id).first():
                    session.add(UserProject(user_id=user.id, project_id=project.id))
        session.commit()

def seed_evaluations():
    with get_session() as session:
        # Fetch all required data upfront
        users = session.query(User).all()
        projects = session.query(Project).all()
        roles = session.query(Role).all()

        batch_size = 100  # Process evaluations in batches
        current_batch = []

        # Get user 1 specifically
        user_1 = session.query(User).filter_by(id=1).first()
        if user_1:
            print(f"Creating diverse evaluations for user: {user_1.name} (ID: {user_1.id})")

            # Get competencies mapped to user_1's role
            user_1_competencies = (
                session.query(Competency)
                .join(CompetencyRoleMap)
                .where(CompetencyRoleMap.role_id == user_1.role_id)
                .all()
            )

            # Get questions only for user_1's role competencies
            questions = (
                session.query(EvaluationQuestion)
                .join(Competency)
                .join(CompetencyRoleMap)
                .where(CompetencyRoleMap.role_id == user_1.role_id)
                .all()
            )

            # Group questions by competency_id and behavior_type for faster access
            questions_by_competency = {}
            for q in questions:
                if q.competency_id not in questions_by_competency:
                    questions_by_competency[q.competency_id] = []
                questions_by_competency[q.competency_id].append(q)

            # Create evaluations for user 1 for each project
            for project in projects:
                print(f"Creating evaluation for project: {project.name} (ID: {project.id})")

                # Create self-evaluation for user 1 for this project with varied responses
                self_eval = Evaluation(
                    evaluation_type=EvaluationType.PERFORMANCE,
                    evaluator_type=EvaluatorType.SELF,
                    evaluator_id=user_1.id,
                    evaluatee_id=user_1.id,
                    project_id=project.id,
                    status=EvaluationStatus.DRAFT if random.random() < 0.5 else EvaluationStatus.SUBMITTED,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow(),
                    evaluatee_role_id=user_1.role_id  # Add evaluatee's role at evaluation time
                )
                session.add(self_eval)
                session.flush()  # Get the ID

                # Create answers with varied responses based on behavior type
                answers = []
                for question in questions:
                    # Determine response based on behavior type
                    if question.behavior_type == BehaviorType.EXPERT:
                        # Expert behavior - 70% positive
                        rand = random.random()
                        if rand < 0.7:
                            response = ResponseType.SI
                        elif rand < 0.85:  # 15% sometimes
                            response = ResponseType.A_VECES
                        else:  # 15% no
                            response = ResponseType.NO
                    elif question.behavior_type == BehaviorType.TALENTED:
                        # Talented behavior - 50% positive
                        rand = random.random()
                        if rand < 0.5:
                            response = ResponseType.SI
                        elif rand < 0.7:  # 20% sometimes
                            response = ResponseType.A_VECES
                        else:  # 30% no
                            response = ResponseType.NO
                    else:
                        # Low skill behavior - 30% positive
                        rand = random.random()
                        if rand < 0.3:
                            response = ResponseType.SI
                        elif rand < 0.6:  # 30% sometimes
                            response = ResponseType.A_VECES
                        else:  # 40% no
                            response = ResponseType.NO

                    # Simple, realistic self-evaluation comments
                    simple_comments = [
                        "Creo que lo hago bien en general",
                        "Necesito mejorar en esto",
                        "Me siento cómodo con esta área",
                        "A veces me cuesta un poco",
                        "Es una de mis fortalezas",
                        "Tengo que trabajar más en esto",
                        "Lo manejo bastante bien",
                        "Me falta práctica aquí",
                        "Estoy mejorando poco a poco",
                        "Se me da natural",
                        "Necesito más experiencia",
                        "Lo hago cuando es necesario"
                    ]

                    answer = EvaluationAnswer(
                        evaluation_id=self_eval.id,
                        question_id=question.id,
                        response=response,
                        comment=random.choice(simple_comments)
                    )
                    answers.append(answer)

                session.bulk_save_objects(answers)
                current_batch.append(self_eval.id)

                # Create peer evaluations from other users to user 1 with varied patterns
                project_users = [
                    up.user for up in session.query(UserProject)
                    .filter_by(project_id=project.id)
                    .all() if up.user.id != user_1.id
                ]

                # Create peer evaluations with different patterns for each peer
                for idx, peer in enumerate(project_users[:3]):  # Increase to 3 peers per project
                    peer_eval = Evaluation(
                        evaluation_type=EvaluationType.PERFORMANCE,
                        evaluator_type=EvaluatorType.PEER,
                        evaluator_id=peer.id,
                        evaluatee_id=user_1.id,
                        project_id=project.id,
                        status=EvaluationStatus.SUBMITTED,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        evaluatee_role_id=user_1.role_id  # Add evaluatee's role at evaluation time
                    )
                    session.add(peer_eval)
                    session.flush()

                    # Create answers with different patterns for each peer
                    peer_answers = []
                    for question in questions:
                        # Adjust response probability based on peer's role
                        peer_role = session.query(Role).filter_by(id=peer.role_id).first()
                        if peer_role.name in ["Analista", "Consultor"]:
                            # Junior roles - better at technical, worse at leadership
                            if question.competency.category == CompetencyCategory.COMPETENCIAS_TECNICAS:
                                # 85% positive
                                rand = random.random()
                                if rand < 0.85:
                                    response = ResponseType.SI
                                elif rand < 0.95:  # 10% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 5% no
                                    response = ResponseType.NO
                            else:
                                # 50% positive
                                rand = random.random()
                                if rand < 0.5:
                                    response = ResponseType.SI
                                elif rand < 0.7:  # 20% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 30% no
                                    response = ResponseType.NO
                        else:
                            # Senior roles - better at leadership, mixed at technical
                            if question.competency.category == CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES:
                                # 90% positive
                                rand = random.random()
                                if rand < 0.9:
                                    response = ResponseType.SI
                                elif rand < 0.95:  # 5% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 5% no
                                    response = ResponseType.NO
                            else:
                                # 70% positive
                                rand = random.random()
                                if rand < 0.7:
                                    response = ResponseType.SI
                                elif rand < 0.85:  # 15% sometimes
                                    response = ResponseType.A_VECES
                                else:  # 15% no
                                    response = ResponseType.NO

                        # Simple, realistic peer evaluation comments
                        peer_comments = [
                            "Trabaja muy bien en equipo",
                            "Siempre está dispuesto a ayudar",
                            "Tiene buen conocimiento técnico",
                            "A veces le cuesta comunicar ideas",
                            "Es muy organizado en sus tareas",
                            "Podría ser más proactivo",
                            "Maneja bien la presión",
                            "Necesita más confianza",
                            "Es muy detallista",
                            "Buen compañero de trabajo",
                            "Le falta experiencia en esto",
                            "Siempre cumple los plazos"
                        ]

                        answer = EvaluationAnswer(
                            evaluation_id=peer_eval.id,
                            question_id=question.id,
                            response=response,
                            comment=random.choice(peer_comments)
                        )
                        peer_answers.append(answer)

                    session.bulk_save_objects(peer_answers)
                    current_batch.append(peer_eval.id)

                # Process batch if it reaches the batch size
                if len(current_batch) >= batch_size:
                    session.commit()
                    process_evaluation_batch(current_batch, session)
                    current_batch = []

        # Generate random evaluations for other users
        for project in projects:
            project_users = [
                up.user for up in session.query(UserProject)
                .filter_by(project_id=project.id)
                .all() if up.user.id != 1  # Skip user 1 as we've already created custom evals
            ]

            for user in project_users:
                # Get competencies mapped to user's role
                user_competencies = (
                    session.query(Competency)
                    .join(CompetencyRoleMap)
                    .where(CompetencyRoleMap.role_id == user.role_id)
                    .all()
                )

                # Get questions only for user's role competencies
                user_questions = (
                    session.query(EvaluationQuestion)
                    .join(Competency)
                    .join(CompetencyRoleMap)
                    .where(CompetencyRoleMap.role_id == user.role_id)
                    .all()
                )

                # Self evaluation (50% chance)
                if random.random() < 0.50:
                    self_eval = Evaluation(
                        evaluation_type=EvaluationType.PERFORMANCE,
                        evaluator_type=EvaluatorType.SELF,
                        evaluator_id=user.id,
                        evaluatee_id=user.id,
                        project_id=project.id,
                        status=EvaluationStatus.DRAFT if random.random() < 0.5 else EvaluationStatus.SUBMITTED,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        evaluatee_role_id=user.role_id  # Add evaluatee's role at evaluation time
                    )
                    session.add(self_eval)
                    session.flush()  # Get the ID

                    # Create all answers for this evaluation at once
                    answers = []
                    for question in user_questions:
                        # 80% positive, 10% sometimes, 10% no
                        rand = random.random()
                        if rand < 0.8:
                            response = ResponseType.SI
                        elif rand < 0.9:
                            response = ResponseType.A_VECES
                        else:
                            response = ResponseType.NO

                        # Simple, realistic self-evaluation comments
                        self_comments = [
                            "Creo que lo hago bien",
                            "Necesito mejorar esto",
                            "Me siento cómodo",
                            "A veces me cuesta",
                            "Es mi fuerte",
                            "Tengo que practicar más",
                            "Lo manejo bien",
                            "Me falta experiencia",
                            "Estoy mejorando",
                            "Se me da bien",
                            "Necesito ayuda aquí",
                            "Lo hago cuando puedo"
                        ]

                        answer = EvaluationAnswer(
                            evaluation_id=self_eval.id,
                            question_id=question.id,
                            response=response,
                            comment=random.choice(self_comments)
                        )
                        answers.append(answer)

                    session.bulk_save_objects(answers)
                    current_batch.append(self_eval.id)

                    # Process batch if it reaches the batch size
                    if len(current_batch) >= batch_size:
                        session.commit()
                        process_evaluation_batch(current_batch, session)
                        current_batch = []

        # Process any remaining evaluations
        if current_batch:
            session.commit()
            process_evaluation_batch(current_batch, session)

    print("Evaluations and scores seeded with diverse patterns.")

def process_evaluation_batch(eval_ids: List[int], session: Session):
    """Process a batch of evaluations to calculate their scores."""
    for eval_id in eval_ids:
        try:
            # Calculate all scores in one transaction
            comp_scores = calculate_competency_scores(eval_id, session)
            session.bulk_save_objects(comp_scores)

            factor_scores = calculate_factor_scores_for_evaluation(eval_id, session)
            if factor_scores:
                session.bulk_save_objects(factor_scores)

            category_scores = calculate_category_scores_for_evaluation(eval_id, session)
            if category_scores:
                session.bulk_save_objects(category_scores)

            session.commit()
            print(f"✓ Processed scores for evaluation {eval_id}")

        except Exception as e:
            print(f"✗ Error processing scores for evaluation {eval_id}: {str(e)}")
            session.rollback()
            continue

def seed_action_plans():
    """Seed comprehensive action plans for user ID 1 (Bruno Bolla Pons)."""
    with get_session() as session:
        # Get user 1 (Bruno Bolla Pons)
        user_1 = session.get(User, 1)
        if not user_1:
            print("User 1 not found. Please run seed_users_and_projects() first.")
            return

        # Get evaluations for user 1 where he is the evaluatee
        evaluations = session.exec(
            select(Evaluation).where(Evaluation.evaluatee_id == 1)
        ).all()

        if not evaluations:
            print("No evaluations found for user 1. Please run seed_evaluations() first.")
            return

        # Use the first evaluation for action plans
        evaluation = evaluations[0]
        print(f"Creating action plans for evaluation ID: {evaluation.id}")

        # Simple, realistic action plans that sound user-generated
        action_plans_data = {
            # Competencias Comportamentales
            "Competencias Comportamentales": {
                "Desarrollo personal": {
                    "category": CompetencyCategory.COMPETENCIAS_COMPORTAMENTALES,
                    "text": """Hemos hablado sobre tu desarrollo personal y creo que hay algunas áreas donde puedes mejorar.

Primero, sería bueno que pidas más feedback a tus compañeros. Tal vez una vez al mes puedas preguntarles cómo ven tu trabajo y qué podrías hacer mejor.

También he notado que a veces te cuesta adaptarte cuando cambian las cosas en los proyectos. Podrías intentar ser más flexible y ver los cambios como oportunidades de aprender algo nuevo.

Para seguir creciendo, te recomiendo que hagas algún curso online. Hay muchos gratuitos que te pueden ayudar. Dedica una hora a la semana a aprender algo nuevo.

Creo que si trabajas en estas cosas durante los próximos meses vas a ver una gran diferencia. Estoy aquí para ayudarte si necesitas algo."""
                }
            },

            # Competencias Técnicas
            "Competencias Tecnicas": {
                "Bloque 0: Preparar el Engagement": {
                    "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "text": """Necesitas mejorar cómo preparas los proyectos antes de empezar. He visto que a veces no conoces bien el sector del cliente o no entiendes completamente lo que necesitan.

Te sugiero que antes de cada proyecto nuevo dediques tiempo a investigar sobre la industria del cliente. Lee noticias, informes, habla con gente que conozca el sector.

También sería bueno que aprendas algunos frameworks básicos de consultoría. Hay muchos recursos online gratuitos que explican cómo analizar empresas y mercados.

Cuando tengas dudas sobre el cliente o el proyecto, no dudes en preguntar a los seniors. Es mejor preguntar antes que tener problemas después.

Practica esto en los próximos proyectos y verás cómo mejora tu trabajo."""
                },

                "Bloque 1: Enmarcar el Problema": {
                    "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "text": """Una de las cosas más importantes en consultoría es saber definir bien el problema que estamos resolviendo. He notado que a veces te pierdes un poco en esta parte.

Mi consejo es que siempre hagas muchas preguntas al principio. No tengas miedo de preguntar lo que no entiendes. Es mejor aclarar todo desde el inicio.

Cuando tengas un problema complejo, trata de dividirlo en partes más pequeñas. Así es más fácil de entender y resolver.

También es útil que escribas tus ideas y las compartas con el equipo. A veces hablando con otros se te ocurren cosas que no habías pensado.

Practica esto en cada proyecto y poco a poco te va a salir más natural."""
                },

                "Bloque 2: Diseñar el Analisis": {
                    "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "text": """Tienes que mejorar cómo diseñas los análisis para los proyectos. A veces no está claro qué quieres demostrar o cómo vas a llegar a las conclusiones.

Antes de empezar cualquier análisis, piensa bien qué pregunta estás tratando de responder. Escríbelo en una frase simple.

Luego planifica qué datos necesitas y cómo los vas a analizar. No te compliques mucho, mantén las cosas simples.

Si no estás seguro de tu enfoque, pide ayuda a alguien más senior. Es mejor revisar el plan antes de empezar que tener que rehacerlo después.

También aprende a usar mejor Excel o alguna herramienta de análisis. Hay muchos tutoriales online que te pueden ayudar."""
                },

                "Bloque 3: Recopilar Datos": {
                    "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "text": """He notado que a veces tienes problemas para conseguir y organizar los datos que necesitas para los análisis. Esto es muy importante porque sin buenos datos no puedes hacer un buen trabajo.

Cuando pidas datos al cliente, sé muy específico sobre lo que necesitas. No digas solo "necesito datos de ventas", sino "necesito datos de ventas por mes, por producto y por región de los últimos dos años".

Una vez que tengas los datos, dedica tiempo a revisarlos bien. Busca si hay números raros, fechas que no cuadran o información que falta. Es mejor detectar estos problemas al principio.

Organiza todo en Excel de forma clara. Usa pestañas diferentes para datos originales y datos limpios. Así si algo sale mal puedes volver atrás.

Siempre guarda una copia de los datos originales sin tocar. Nunca sabes cuándo los vas a necesitar."""
                },

                "Bloque 4: Interpretar los Resultados": {
                    "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "text": """Una vez que tienes los resultados del análisis, necesitas saber qué significan realmente. He visto que a veces te quedas solo con los números sin entender la historia que cuentan.

Cuando veas los resultados, pregúntate siempre por qué están pasando las cosas. Si las ventas bajaron, no te quedes ahí, trata de entender qué pudo haber causado esa bajada.

Compara los resultados con lo que esperabas al principio. Si algo es muy diferente, revisa si puede haber un error o si realmente está pasando algo interesante.

No tengas miedo de decir cuando algo no está claro o cuando necesitas más información para entender mejor. Es mejor ser honesto que inventar explicaciones.

Siempre piensa en qué le importa al cliente. Los números están bien, pero lo que realmente importa es qué puede hacer el cliente con esa información."""
                },

                "Bloque 5: Presentación de ideas": {
                    "category": CompetencyCategory.COMPETENCIAS_TECNICAS,
                    "text": """Tienes que mejorar cómo presentas tus análisis. A veces haces un buen trabajo pero no sabes comunicarlo bien y se pierde el valor de lo que hiciste.

Cuando hagas una slide, el título tiene que decir algo importante, no solo describir lo que hay. En lugar de "Ventas por región" pon "Las ventas del norte crecieron 15% más que las del sur".

Mantén las slides simples. Una idea principal por slide. Si intentas meter mucha información, la gente se confunde y no entiende el mensaje.

Antes de presentar, practica explicando tus resultados en voz alta. Así te das cuenta si algo no se entiende bien.

Prepárate para preguntas. Piensa qué te podrían preguntar sobre tu análisis y ten las respuestas listas. Si no sabes algo, está bien decir que lo vas a revisar."""
                }
            },

            # Aprendizaje
            "Aprendizaje": {
                "Desarrollo personal": {
                    "category": CompetencyCategory.APRENDIZAJE,
                    "text": """Me gusta que tengas ganas de aprender, pero creo que puedes ser más sistemático en tu desarrollo.

Te recomiendo que dediques un poco de tiempo cada semana a aprender algo nuevo. No tiene que ser mucho, con 30 minutos ya está bien.

Busca cursos online sobre temas que te interesen o que te ayuden en tu trabajo. Hay muchas plataformas gratuitas con contenido muy bueno.

También sería bueno que reflexiones sobre lo que aprendes en cada proyecto. Al final de cada semana, piensa qué cosas nuevas aprendiste y qué podrías hacer mejor la próxima vez.

Habla con gente de otros equipos para conocer cómo trabajan. Siempre se puede aprender algo de los demás.

Si sigues estos consejos, vas a crecer mucho profesionalmente."""
                }
            }
        }

        # Create action plans for each category/factor
        created_count = 0
        for category_name, factors in action_plans_data.items():
            for factor_name, plan_data in factors.items():
                # Check if action plan already exists
                existing_plan = session.exec(
                    select(ActionPlan).where(
                        ActionPlan.evaluation_id == evaluation.id,
                        ActionPlan.factor == factor_name,
                        ActionPlan.category == plan_data["category"]
                    )
                ).first()

                if not existing_plan:
                    action_plan = ActionPlan(
                        evaluation_id=evaluation.id,
                        category=plan_data["category"],
                        factor=factor_name,
                        action_plan_text=plan_data["text"],
                        evaluator_id=3,  # Carlos Ruiz Martínez (main evaluator)
                        created_at=datetime.now(timezone.utc)
                    )
                    session.add(action_plan)
                    created_count += 1
                    print(f"Created action plan for {category_name} - {factor_name}")

        session.commit()
        print(f"Successfully created {created_count} action plans for user {user_1.name}")

if __name__ == "__main__":
    seed_roles()
    seed_performance_competencies()
    seed_potential_competencies()
    seed_competency_role_map_from_json()
    seed_users_and_projects()
    seed_evaluations()
    seed_action_plans()
